CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_cjl`(IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(50)
)
    COMMENT '单次超极量分析存储过程'
main_block: BEGIN
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_sda_tj VARCHAR(20);
		DECLARE v_sda_drcs VARCHAR(20);
		DECLARE v_sda_dcyl VARCHAR(20);
		DECLARE v_yl_unit1 VARCHAR(20);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_n_count INT;
		DECLARE v_gydw_converted VARCHAR(20);

		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				DECLARE v_sqlstate CHAR(5);
				DECLARE v_errno INT;
				DECLARE v_text TEXT;

				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;

				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;

		-- 检查药品状态
		SELECT COUNT(1) INTO v_n_count
		FROM rms_ITF_HOS_DRUG
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';

		IF v_n_count > 0 THEN
				LEAVE main_block;
		END IF;

		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name
		FROM rms_ITF_HOS_DRUG
		WHERE DRUG_CODE = p_yp_code LIMIT 1;

		-- 获取标准药品ID
		SELECT sda_id INTO v_sda_id
		FROM rms_t_byyydzb
		WHERE akb020 = p_akb020 AND yp_code = p_yp_code LIMIT 1;

		-- 获取剂型编码
		SELECT by_code INTO v_sda_tj
		FROM rms_t_tjdzb
		WHERE h_tj = p_yp_tj AND akb020 = p_akb020 LIMIT 1;

		-- 获取频次信息
		SELECT drcs INTO v_sda_drcs
		FROM rms_t_pcdmb
		WHERE his_pc = p_gypc AND akb020 = p_akb020 LIMIT 1;

		-- 获取用量单位
		SELECT DISTINCT UnitRem INTO v_yl_unit1
		FROM rms_t_sda
		WHERE id = v_sda_id LIMIT 1;

		-- 单位转换
		SET v_gydw_converted = p_gydw;
		IF p_gydw = 'g' THEN
				SET v_gydw_converted = '克';
		ELSEIF p_gydw = 'mg' THEN
				SET v_gydw_converted = '毫克';
		ELSEIF p_gydw = 'ml' THEN
				SET v_gydw_converted = '毫升';
		END IF;

		-- 单位不匹配则退出
		IF v_gydw_converted != v_yl_unit1 THEN
				LEAVE main_block;
		END IF;

		-- 单次超极量分析结果
		INSERT INTO rms_t_pres_fx
		SELECT DISTINCT
				p_code,
				v_ywa_name AS ywa,
				'' AS ywb,
				'1' AS wtlvlcode,
				'重要警示' AS wtlvl,
				'RLT010' AS wtcode,
				'CJLJJ' AS wtsp,
				'超极量' AS wtname,
				CONCAT('【', CAST(c.tymc AS CHAR), '】单次用量超最大次极量') AS title,
				CONCAT('说明书提示：', CAST(c.tymc AS CHAR), '++++', '单次最大极量为：', CAST(a.maxcount AS CHAR)) AS detail,
				0,
				'超极量'
		FROM rms_t_sda c
		LEFT JOIN rms_t_sda_max a ON a.sda_id = c.ID AND a.jlbs = '0'
		WHERE c.ID = v_sda_id
				AND SUBSTRING(a.gytj_code, 1, 2) = SUBSTRING(v_sda_tj, 1, 2)
				AND CAST(a.maxcount AS DECIMAL(14,4)) < CAST(p_dcsl AS DECIMAL(14,4));

END
